import argparse
import json
import re
from pathlib import Path
from typing import List, Dict, Any

from test_halldet.datasets.hallucination_dataset import HallucinationData, HallucinationSample


def parse_hallucination_spans(hal_span: str, answer: str) -> List[Dict[str, Any]]:
    """Parse hallucination spans from the hal_span field.
    
    Args:
        hal_span: String containing hallucinated text marked with <HAL> tags
        answer: The full answer text
        
    Returns:
        List of label dictionaries with start, end, and label fields
    """
    labels = []
    
    if not hal_span or hal_span.strip() == "":
        return labels
    
    # Find all <HAL>...</HAL> patterns in hal_span
    hal_pattern = r'<HAL>(.*?)</HAL>'
    hal_matches = re.findall(hal_pattern, hal_span, re.DOTALL)
    
    for hal_text in hal_matches:
        # Find the position of this hallucinated text in the answer
        hal_text_clean = hal_text.strip()
        if hal_text_clean in answer:
            start_pos = answer.find(hal_text_clean)
            end_pos = start_pos + len(hal_text_clean)
            
            labels.append({
                "start": start_pos,
                "end": end_pos,
                "label": "hallucination"
            })
        else:
            # If exact match not found, try to find partial matches
            # This handles cases where the hal_span might have slight formatting differences
            words = hal_text_clean.split()
            for i in range(len(words)):
                for j in range(i + 1, len(words) + 1):
                    phrase = " ".join(words[i:j])
                    if phrase in answer and len(phrase) > 3:  # Only consider phrases longer than 3 chars
                        start_pos = answer.find(phrase)
                        end_pos = start_pos + len(phrase)
                        
                        labels.append({
                            "start": start_pos,
                            "end": end_pos,
                            "label": "hallucination"
                        })
                        break
    
    return labels


def create_sample_from_custom_format(item: Dict[str, Any], split: str = "train") -> HallucinationSample:
    """Create a HallucinationSample from custom dataset format.
    
    Args:
        item: Dictionary containing custom dataset fields
        split: Dataset split (train/dev/test)
        
    Returns:
        HallucinationSample object
    """
    # Combine context into a single prompt
    context_parts = item.get("context", [])
    if isinstance(context_parts, list):
        context_text = " ".join(context_parts)
    else:
        context_text = str(context_parts)
    
    question = item.get("question", "")
    prompt = f"Context: {context_text}\n\nQuestion: {question}"
    
    answer = item.get("answer", "")
    hal_flag = item.get("hal", 0)
    hal_span = item.get("hal_span", "")
    
    # Parse hallucination labels
    if hal_flag == 1 and hal_span:
        labels = parse_hallucination_spans(hal_span, answer)
    else:
        labels = []
    
    return HallucinationSample(
        prompt=prompt,
        answer=answer,
        labels=labels,
        split=split,
        task_type="qa",  # Default task type for Q&A format
        dataset="custom",
        language="en"
    )


# Fixed split ratios for consistency across all datasets
TRAIN_RATIO = 0.8
DEV_RATIO = 0.1
# TEST_RATIO = 0.1 (implicit: 1.0 - TRAIN_RATIO - DEV_RATIO)

def split_dataset(samples: List[HallucinationSample]) -> List[HallucinationSample]:
    """Split dataset into train/dev/test using fixed ratios (80/10/10).

    Args:
        samples: List of samples to split

    Returns:
        List of samples with updated split assignments
    """
    total = len(samples)
    train_end = int(total * TRAIN_RATIO)
    dev_end = train_end + int(total * DEV_RATIO)
    
    # Assign splits
    for i, sample in enumerate(samples):
        if i < train_end:
            sample.split = "train"
        elif i < dev_end:
            sample.split = "dev"
        else:
            sample.split = "test"
    
    return samples


def main():
    parser = argparse.ArgumentParser(description="Preprocess custom dataset for hallucination detection")
    parser.add_argument(
        "--input-file",
        type=str,
        required=True,
        help="Path to the input JSONL file"
    )
    parser.add_argument(
        "--output-file",
        type=str,
        required=True,
        help="Path to the output JSON file"
    )
    
    args = parser.parse_args()
    
    input_path = Path(args.input_file)
    output_path = Path(args.output_file)
    
    if not input_path.exists():
        raise FileNotFoundError(f"Input file not found: {args.input_file}")
    
    # Create output directory if it doesn't exist
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    print(f"Loading data from {input_path}")
    
    # Load JSONL data
    samples = []
    with open(input_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                item = json.loads(line.strip())
                sample = create_sample_from_custom_format(item)
                samples.append(sample)
            except json.JSONDecodeError as e:
                print(f"Warning: Skipping invalid JSON on line {line_num}: {e}")
            except Exception as e:
                print(f"Warning: Error processing line {line_num}: {e}")
    
    print(f"Loaded {len(samples)} samples")

    # Split dataset using fixed ratios (80/10/10)
    samples = split_dataset(samples)
    
    # Count splits
    train_count = sum(1 for s in samples if s.split == "train")
    dev_count = sum(1 for s in samples if s.split == "dev")
    test_count = sum(1 for s in samples if s.split == "test")

    print(f"Dataset split (80/10/10): {train_count} train, {dev_count} dev, {test_count} test")
    
    # Count hallucinated samples
    hal_count = sum(1 for s in samples if len(s.labels) > 0)
    print(f"Samples with hallucinations: {hal_count} ({hal_count/len(samples)*100:.1f}%)")
    
    # Create HallucinationData object and save
    hallucination_data = HallucinationData(samples=samples)
    
    print(f"Saving preprocessed data to {output_path}")
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(hallucination_data.to_json(), f, indent=2, ensure_ascii=False)
    
    print("Preprocessing completed successfully!")


if __name__ == "__main__":
    main()
