[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "test_halldet"
version = "0.1.7"
description = "Test_HallDet is a framework for detecting hallucinations."
readme = {file = "README.md", content-type = "text/markdown"}
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "torch>=2.6.0",
    "transformers>=4.48.3",
    "tqdm>=4.65.0",
    "scikit-learn>=1.6.1",
    "numpy>=2.2.2",
    "openai==1.66.3",
]

[project.urls]
Homepage = "https://github.com/krlabsorg/test_halldet"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.25",
    "ruff>=0.0.270",
]
api = [
    "fastapi[standard]>=0.115",
    "pydantic-settings>=2.8.0",
    "httpx>=0.28"
]

[tool.setuptools]
packages = ["test_halldet", "test_halldet_api"]

[tool.pytest]
testpaths = ["tests"]
python_files = "test_*_pytest.py"

[tool.ruff]
line-length = 100

[tool.ruff.lint]
# https://docs.astral.sh/ruff/rules/
select = [
    "E",   # flake8
    "F",   # pyflakes
    "I",   # isort
    "C90", # mccabe
    "D",   # pydocstyle
    "ANN", # type annotations
    "S",   # bandit
    "EXE", # flake8 executable
    "PTH", # use pathlib
    "RUF", # ruff rules
]
ignore = [
    "E501",   # line length
    "D100",   # module docstring
    "D104",   # missing docstring in public package
    "D203",   # blank line required before class
    "D211",   # no blank line before class
    "D213",   # multi line summary second line
    "ANN003", # **kwargs annotation
    "ANN204", # missing return type for __init__
    "PTH123", # path.open
]

[tool.ruff.lint.per-file-ignores]
"test_halldet_api/test_server.py" = ["S101"]
"test_halldet_api/test_client.py" = ["S101"]