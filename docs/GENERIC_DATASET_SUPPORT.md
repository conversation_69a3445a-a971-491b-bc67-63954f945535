# Generic Dataset Support for Test_HallDet

This document describes the modifications made to Test_HallDet to support generic datasets for binary classification tasks, including context length extension and custom dataset preprocessing.

## Overview of Changes

The following modifications have been made to support generic datasets:

1. **Extended Context Length**: Increased from 4096 to 8192 tokens
2. **Generic Training Pipeline**: Modified train.py and evaluate.py to work with any preprocessed dataset
3. **Custom Dataset Preprocessor**: Created preprocessor for custom JSONL format
4. **Backward Compatibility**: Maintained support for existing RAGTruth/RAGBench datasets

## Context Length Extension

### Changes Made
- Modified `HallucinationDataset` class in `lettucedetect/datasets/hallucination_dataset.py`
- Updated default `max_length` parameter from 4096 to 8192 tokens
- Both the constructor and `prepare_tokenized_input` method now use 8192 as default

### Impact
- Models can now process longer contexts (up to 8192 tokens)
- Better support for documents with extensive context information
- Improved performance on tasks requiring longer input sequences

## Generic Dataset Support

### Modified Scripts

#### train.py
- **Arguments**:
  - `--data-path`: Dataset path (required)
- **Features**:
  - Automatic dataset loading and validation
  - Flexible train/dev splitting

#### evaluate.py
- **Enhanced Data Loading**:
  - Improved error handling for missing files
  - Automatic fallback if no test samples found
  - Robust task_type handling for datasets without this field

### Usage Examples

```bash
# Train with custom dataset
python scripts/train.py --data-path data/custom_dataset_processed.json

# Evaluate model
python scripts/evaluate.py \
    --model_path output/hallucination_detector \
    --data_path data/custom_dataset_processed.json
```

## Custom Dataset Format

### Input Format (JSONL)
Your custom dataset should be in JSONL format with the following fields:

```json
{
    "context": ["Context paragraph 1", "Context paragraph 2", ...],
    "question": "What is the question being asked?",
    "answer": "The model's response to the question",
    "hal": 0,  // 0 = no hallucination, 1 = has hallucination
    "hal_span": ""  // Empty for non-hallucinated, or contains <HAL>text</HAL> markers
}
```

### Hallucination Annotation
For samples with hallucinations (`hal: 1`), mark the hallucinated text in `hal_span`:

```json
{
    "hal": 1,
    "hal_span": "The answer contains <HAL>incorrect information</HAL> that should be detected."
}
```

### Preprocessing Your Dataset

Use the provided preprocessor to convert your custom format:

```bash
# Basic preprocessing
python scripts/preprocess_custom_dataset_standalone.py \
    --input-file data/your_dataset.jsonl \
    --output-file data/your_dataset_processed.json

# Custom train/dev/test splits
python scripts/preprocess_custom_dataset_standalone.py \
    --input-file data/your_dataset.jsonl \
    --output-file data/your_dataset_processed.json \
    --train-ratio 0.7 \
    --dev-ratio 0.15
```

## Expected Output Format

The preprocessor converts your data to the `HallucinationSample` format:

```json
{
    "prompt": "Context: ... Question: ...",
    "answer": "The model's response",
    "labels": [
        {
            "start": 10,
            "end": 25,
            "label": "hallucination"
        }
    ],
    "split": "train",
    "task_type": "qa",
    "dataset": "custom",
    "language": "en"
}
```

### Required Fields
- `prompt`: Combined context and question
- `answer`: The response text to analyze
- `labels`: List of hallucination spans with start/end positions
- `split`: Dataset split ("train", "dev", or "test")
- `task_type`: Type of task (defaults to "qa")
- `dataset`: Dataset identifier (defaults to "custom")
- `language`: Language code (defaults to "en")

## Data Requirements

### Minimum Requirements
1. **Context**: Provide relevant context information
2. **Question/Prompt**: Clear question or instruction
3. **Answer**: Model response to analyze
4. **Hallucination Labels**: Binary flag and span annotations

### Recommended Practices
1. **Balanced Dataset**: Include both hallucinated and non-hallucinated samples
2. **Quality Annotations**: Ensure accurate hallucination span marking
3. **Diverse Content**: Include various types of questions and contexts
4. **Sufficient Size**: Minimum 1000 samples recommended for training
5. **Dataset Combination**: If you need to combine multiple datasets, do so during preprocessing rather than training

## Testing Your Dataset

Use the provided test script to validate your processed dataset:

```bash
python scripts/test_dataset_loading.py
```

This will verify:
- Dataset structure and format
- Split distributions
- Hallucination sample statistics
- Required field presence

## Troubleshooting

### Common Issues

1. **Import Errors**: Install required dependencies:
   ```bash
   pip install torch transformers tqdm scikit-learn numpy
   ```

2. **Memory Issues**: Reduce batch size in training:
   ```bash
   python scripts/train.py --data-path data/custom.json --batch-size 2
   ```

3. **Context Too Long**: The 8192 token limit should handle most cases, but very long documents may still be truncated.

4. **Need to Combine Datasets**: The training script now only accepts a single dataset. To combine multiple datasets, merge them during preprocessing before training.

### Validation Checklist
- [ ] Dataset loads without errors
- [ ] Hallucination spans are correctly positioned
- [ ] Train/dev/test splits are reasonable
- [ ] Sample count matches expectations
- [ ] Required fields are present

## Migration from RAGTruth

If migrating from RAGTruth-specific code:

1. **Update Training Commands**:
   ```bash
   # Old (no longer supported)
   # python scripts/train.py --ragtruth-path data/ragtruth_data.json

   # New
   python scripts/train.py --data-path data/ragtruth_data.json
   ```

2. **Update Evaluation Commands**:
   ```bash
   # Old
   python scripts/evaluate.py --data_path data/ragtruth_data.json
   
   # New (same, but more robust)
   python scripts/evaluate.py --data_path data/ragtruth_data.json
   ```

## Performance Considerations

- **Context Length**: 8192 tokens require more memory and computation
- **Batch Size**: May need to reduce batch size for longer contexts
- **Training Time**: Longer contexts increase training time
- **GPU Memory**: Monitor GPU memory usage with longer sequences

## Summary of Deliverables

✅ **Context Length Extension**: Extended from 4096 to 8192 tokens
✅ **Generic Dataset Support**: Modified train.py and evaluate.py for any dataset
✅ **Custom Dataset Preprocessor**: Created preprocessor for your specific JSONL format
✅ **Backward Compatibility**: Existing RAGTruth/RAGBench workflows still work
✅ **Documentation**: Complete usage guide and troubleshooting

## Quick Start

1. **Preprocess your dataset**:
   ```bash
   python scripts/preprocess_custom_dataset_standalone.py \
       --input-file data/dataset_with_hal.jsonl \
       --output-file data/custom_dataset_processed.json
   ```

2. **Train the model**:
   ```bash
   python scripts/train.py --data-path data/custom_dataset_processed.json
   ```

3. **Evaluate the model**:
   ```bash
   python scripts/evaluate.py \
       --model_path output/hallucination_detector \
       --data_path data/custom_dataset_processed.json
   ```

## Future Enhancements

Potential improvements for future versions:
- Dynamic context length based on input
- Support for additional annotation formats
- Automated data quality validation
- Multi-language preprocessing support
