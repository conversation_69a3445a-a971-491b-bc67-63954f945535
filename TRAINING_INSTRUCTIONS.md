# HallDet Model Training Instructions

This comprehensive guide provides step-by-step instructions for training and testing the HallDet hallucination detection model from scratch.

## Table of Contents

- [Environment Setup](#environment-setup)
- [Data Preparation](#data-preparation)
- [Model Training](#model-training)
- [Model Evaluation](#model-evaluation)
- [Inference Usage](#inference-usage)
- [Troubleshooting](#troubleshooting)

## Environment Setup

### System Requirements

| Component | Minimum | Recommended |
|-----------|---------|-------------|
| Python | 3.10+ | 3.10+ |
| GPU Memory | 8GB | 16GB+ |
| System RAM | 16GB | 32GB |
| Storage | 10GB | 20GB+ |
| CUDA | 11.8+ | 12.0+ |

### Installation Steps

1. **Clone the Repository**
```bash
git clone https://github.com/krlabsorg/test_halldet.git
cd test_halldet
```

2. **Create Virtual Environment**
```bash
# Using conda (recommended)
conda create -n halldet python=3.10
conda activate halldet

# Or using venv
python -m venv halldet_env
source halldet_env/bin/activate  # On Windows: halldet_env\Scripts\activate
```

3. **Install Dependencies**
```bash
# Install core dependencies
pip install -e .

# Install development dependencies (for testing)
pip install -e ".[dev]"

# Install API dependencies (for web server)
pip install -e ".[api]"

# Or install all optional dependencies
pip install -e ".[dev,api]"
```

4. **Verify Installation**
```bash
python -c "import test_halldet; import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}')"
```

### Hardware Recommendations

#### Training Time Estimates (6 epochs)

| Model | Batch Size | GPU Memory | Training Time |
|-------|------------|------------|---------------|
| ModernBERT-base | 4 | ~8GB | 2-4 hours |
| ModernBERT-base | 8 | ~12GB | 1.5-3 hours |
| ModernBERT-large | 2 | ~16GB | 4-8 hours |
| ModernBERT-large | 4 | ~24GB | 3-6 hours |

## Data Preparation

### RAGTruth Dataset

#### Option 1: Download from Hugging Face
```bash
python scripts/download_dataset.py \
    --repository-id "krlabsorg/ragtruth" \
    --output-path "data/ragtruth/ragtruth_data.json"
```

#### Option 2: Process Raw Files
If you have `response.jsonl` and `source_info.jsonl`:
```bash
python test_halldet/preprocess/preprocess_ragtruth.py \
    --input_dir "data/ragtruth" \
    --output_dir "data/ragtruth"
```

#### Expected RAGTruth Input Format

**response.jsonl** (one JSON per line):
```json
{
  "id": "0",
  "source_id": "15592",
  "labels": [{"start": 10, "end": 25, "label_type": "Evident Conflict"}],
  "split": "train",
  "response": "The generated response text..."
}
```

**source_info.jsonl** (one JSON per line):
```json
{
  "source_id": "15592",
  "task_type": "Summary",
  "prompt": "Summarize the following...",
  "source_info": "Original source text..."
}
```

### Custom Dataset

#### Input Format Requirements

Create a JSONL file where each line contains:
```json
{
  "context": ["Context passage 1", "Context passage 2"],
  "question": "Your question here",
  "answer": "The generated answer",
  "hal": 1,
  "hal_span": "Normal text <HAL>hallucinated content</HAL> more text"
}
```

**Required Fields:**
- `context`: List of context passages or single string
- `question`: The question being answered
- `answer`: The generated response to evaluate
- `hal`: Binary flag (1 = contains hallucination, 0 = no hallucination)
- `hal_span`: Text with hallucinated portions marked with `<HAL>...</HAL>` tags

#### Preprocessing Custom Dataset

```bash
python scripts/preprocess_custom_dataset_standalone.py \
    --input-file "data/custom/your_dataset.jsonl" \
    --output-file "data/custom/processed_dataset.json"
```

**Note:** The preprocessor automatically applies an 80/10/10 train/dev/test split.

### Data Verification

#### Test Dataset Loading
```bash
python scripts/test_dataset_loading.py --data-path "data/ragtruth/ragtruth_data.json"
```

#### Check Data Statistics
```bash
python -c "
import json
from pathlib import Path

data = json.loads(Path('data/ragtruth/ragtruth_data.json').read_text())
print(f'Total samples: {len(data)}')

splits = {}
for sample in data:
    split = sample['split']
    splits[split] = splits.get(split, 0) + 1

print('Split distribution:')
for split, count in splits.items():
    print(f'  {split}: {count} ({count/len(data)*100:.1f}%)')

hal_count = sum(1 for sample in data if len(sample['labels']) > 0)
print(f'Samples with hallucinations: {hal_count} ({hal_count/len(data)*100:.1f}%)')
"
```

**Expected Output:**
```
Total samples: 18000
Split distribution:
  train: 14400 (80.0%)
  dev: 1800 (10.0%)
  test: 1800 (10.0%)
Samples with hallucinations: 9000 (50.0%)
```

## Model Training

### Basic Training Commands

#### Training with RAGTruth Dataset
```bash
python scripts/train.py \
    --data-path "data/ragtruth/ragtruth_data.json" \
    --model-name "answerdotai/ModernBERT-base" \
    --output-dir "output/hallucination_detector_ragtruth" \
    --batch-size 4 \
    --epochs 6 \
    --learning-rate 1e-5
```

#### Training with Custom Dataset
```bash
python scripts/train.py \
    --data-path "data/custom/processed_dataset.json" \
    --model-name "answerdotai/ModernBERT-base" \
    --output-dir "output/hallucination_detector_custom" \
    --batch-size 4 \
    --epochs 8 \
    --learning-rate 2e-5
```

#### Training with ModernBERT-large
```bash
python scripts/train.py \
    --data-path "data/ragtruth/ragtruth_data.json" \
    --model-name "answerdotai/ModernBERT-large" \
    --output-dir "output/hallucination_detector_large" \
    --batch-size 2 \
    --epochs 6 \
    --learning-rate 1e-5
```

### Training Parameters

| Parameter | Description | Recommended Values | Notes |
|-----------|-------------|-------------------|-------|
| `--data-path` | Path to preprocessed JSON dataset | Your dataset file | Required |
| `--model-name` | Base model to fine-tune | `answerdotai/ModernBERT-base` or `answerdotai/ModernBERT-large` | Choose based on resources |
| `--output-dir` | Directory to save trained model | `output/model_name` | Created automatically |
| `--batch-size` | Training batch size | 2-8 | Reduce if out of memory |
| `--epochs` | Number of training epochs | 6-10 | More epochs for custom data |
| `--learning-rate` | Learning rate | 1e-5 to 5e-5 | Start with 1e-5 |

### Monitoring Training Progress

#### Expected Training Output
```
Starting training on cuda
Training samples: 14400, Test samples: 1800

Epoch 1/6
Training: 100%|████████| 3600/3600 [45:32<00:00, 1.32it/s, loss=0.4521, avg_loss=0.4832]
Epoch 1 completed in 0:45:32. Average loss: 0.4832

Evaluating...
  Precision: 0.7234
  Recall: 0.6891
  F1: 0.7058
  Hallucinated F1: 0.7058

🎯 New best F1: 0.7058, model saved at 'output/hallucination_detector'!
```

#### Key Metrics to Monitor
- **Loss**: Should decrease over epochs (target: <0.3)
- **F1 Score**: Should increase over epochs (target: >0.75)
- **Precision**: Accuracy of hallucination predictions
- **Recall**: Coverage of actual hallucinations

## Model Evaluation

### Example-Level Evaluation
Tests whether the model correctly identifies if an answer contains any hallucination:
```bash
python scripts/evaluate.py \
    --model_path "output/hallucination_detector" \
    --data_path "data/ragtruth/ragtruth_data.json" \
    --evaluation_type "example_level" \
    --batch_size 8
```

### Token-Level Evaluation
Evaluates the model's ability to identify individual hallucinated tokens:
```bash
python scripts/evaluate.py \
    --model_path "output/hallucination_detector" \
    --data_path "data/ragtruth/ragtruth_data.json" \
    --evaluation_type "token_level" \
    --batch_size 8
```

### Character-Level Span Evaluation
Tests the model's precision in identifying exact character spans of hallucinations:
```bash
python scripts/evaluate.py \
    --model_path "output/hallucination_detector" \
    --data_path "data/ragtruth/ragtruth_data.json" \
    --evaluation_type "char_level" \
    --batch_size 8
```

### Expected Evaluation Output
```
Evaluating model on test samples: 1800

Task type: qa
---- Example-Level Evaluation ----
  Precision: 0.7891
  Recall: 0.7654
  F1: 0.7771

Task type: whole dataset
---- Token-Level Evaluation ----
  Precision: 0.8123
  Recall: 0.7432
  F1: 0.7762
```

## Inference Usage

### Basic Inference Example

```python
from test_halldet.models.inference import HallucinationDetector

# Load your trained model
detector = HallucinationDetector(
    method="transformer", 
    model_path="output/hallucination_detector"
)

# Test with example data
contexts = [
    "France is a country in Europe. The capital of France is Paris. The population of France is 67 million."
]
question = "What is the capital and population of France?"
answer = "The capital of France is Paris. The population of France is 69 million."

# Get span-level predictions
predictions = detector.predict(
    context=contexts, 
    question=question, 
    answer=answer, 
    output_format="spans"
)

print("Hallucination spans detected:")
for pred in predictions:
    print(f"  Text: '{pred['text']}'")
    print(f"  Confidence: {pred['confidence']:.4f}")
    print(f"  Position: {pred['start']}-{pred['end']}")
```

### Batch Inference Example

```python
# Test multiple examples at once
prompts = [
    "Context: The sky is blue.\n\nQuestion: What color is the sky?",
    "Context: Water boils at 100°C.\n\nQuestion: At what temperature does water boil?"
]
answers = [
    "The sky is green and beautiful.",
    "Water boils at 100 degrees Celsius."
]

batch_predictions = detector.predict_prompt_batch(
    prompts=prompts,
    answers=answers,
    output_format="spans"
)

for i, preds in enumerate(batch_predictions):
    print(f"Example {i+1}: {len(preds)} hallucinations detected")
```

### Expected Inference Output
```
Hallucination spans detected:
  Text: ' The population of France is 69 million.'
  Confidence: 0.9891
  Position: 31-71
```

## Troubleshooting

### Memory Issues

#### CUDA Out of Memory
```
RuntimeError: CUDA out of memory. Tried to allocate 2.00 GiB
```

**Solutions:**
```bash
# Reduce batch size
python scripts/train.py --batch-size 2 [other args]

# Clear GPU cache before training
python -c "import torch; torch.cuda.empty_cache()"
```

### Training Issues

#### Loss Not Decreasing
**Possible Solutions:**
- Reduce learning rate: `--learning-rate 5e-6`
- Verify data preprocessing steps
- Try ModernBERT-base instead of large

#### F1 Score Not Improving
**Solutions:**
```bash
# Try different learning rate
python scripts/train.py --learning-rate 2e-5 [other args]

# Increase training epochs
python scripts/train.py --epochs 10 [other args]
```

### Data Issues

#### Dataset Loading Errors
```bash
# Test dataset format
python scripts/test_dataset_loading.py --data-path "your_data.json"

# Validate JSON format
python -c "import json; json.loads(open('your_data.json').read()); print('Valid JSON')"
```

#### Custom Dataset Preprocessing Errors
**Common Issues:**
- Verify JSONL format (one JSON object per line)
- Check required fields: `context`, `question`, `answer`, `hal`, `hal_span`
- Ensure HAL tags are properly formatted: `<HAL>text</HAL>`

### Performance Issues

#### Training Too Slow
**Solutions:**
- Use GPU instead of CPU
- Increase batch size if memory allows
- Use ModernBERT-base instead of large

#### Poor Model Performance
**Diagnostic Steps:**
1. Check data quality and balance (aim for 40-60% hallucinated samples)
2. Verify preprocessing correctness
3. Try different hyperparameters
4. Increase training epochs
5. Use larger model (ModernBERT-large)

## Best Practices

### Training Tips
1. **Start Small**: Begin with ModernBERT-base and small datasets
2. **Monitor Closely**: Watch loss and F1 metrics during training
3. **Save Checkpoints**: The script automatically saves the best model
4. **Validate Early**: Test on a small subset before full training

### Data Quality Tips
1. **Balance Dataset**: Aim for 40-60% hallucinated samples
2. **Quality Over Quantity**: Better to have fewer high-quality annotations
3. **Diverse Examples**: Include various types of hallucinations
4. **Consistent Annotation**: Use clear guidelines for HAL tag placement

### Production Deployment
1. **Use ModernBERT-large**: For best production performance
2. **Thorough Evaluation**: Test on held-out data
3. **Monitor Performance**: Track metrics in production
4. **Regular Updates**: Retrain with new data periodically

---

For additional support and advanced usage, refer to the project documentation and GitHub issues.
