{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/projects/LettuceDetect/lettucedetect/models/inference.py:84: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  labels = torch.tensor(labels, device=self.device)\n", "Compiling the model with `torch.compile` and using a `torch.cpu` device is not supported. Falling back to non-compiled mode.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Predictions: [{'start': 31, 'end': 71, 'confidence': 0.9891982674598694, 'text': ' The population of France is 69 million.'}]\n"]}], "source": ["from test_halldet.models.inference import HallucinationDetector\n", "\n", "# For a transformer-based approach:\n", "detector = HallucinationDetector(\n", "    method=\"transformer\", model_path=\"KRLabsOrg/lettucedect-base-modernbert-en-v1\"\n", ")\n", "\n", "contexts = [\n", "    \"France is a country in Europe. The capital of France is Paris. The population of France is 67 million.\",\n", "]\n", "question = \"What is the capital of France? What is the population of France?\"\n", "answer = \"The capital of France is Paris. The population of France is 69 million.\"\n", "\n", "# Get span-level predictions indicating which parts of the answer are considered hallucinated.\n", "predictions = detector.predict(\n", "    context=contexts, question=question, answer=answer, output_format=\"spans\"\n", ")\n", "print(\"Predictions:\", predictions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lettucedetect", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}