{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pprint import pprint\n", "\n", "from lettucedetect_api.client import LettuceClient"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = LettuceClient(\"http://127.0.0.1:8000\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["contexts = [\n", "    \"France is a country in Europe. The capital of France is Paris. The population of France is 67 million.\",\n", "]\n", "question = \"What is the capital of France? What is the population of France?\"\n", "answer = \"The capital of France is Paris. The population of France is 69 million.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = client.detect_token(contexts, question, answer)\n", "pprint(response.predictions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = client.detect_spans(contexts, question, answer)\n", "pprint(response.predictions)"]}], "metadata": {"kernelspec": {"display_name": "lettuce", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}