import argparse
import json
import random
from pathlib import Path

import numpy as np
import torch
from torch.utils.data import DataLoader
from transformers import (
    AutoModelForTokenClassification,
    AutoTokenizer,
    DataCollatorForTokenClassification,
)

from test_halldet.datasets.hallucination_dataset import (
    HallucinationData,
    HallucinationDataset,
    HallucinationSample,
)
from test_halldet.models.trainer import Trainer


def set_seed(seed: int = 42):
    """Set all seeds for reproducibility.

    Args:
        seed: The seed to use

    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    # For complete determinism at cost of performance:
    # torch.backends.cudnn.deterministic = True
    # torch.backends.cudnn.benchmark = False


def parse_args():
    parser = argparse.ArgumentParser(description="Train hallucination detector model")
    parser.add_argument(
        "--data-path",
        type=str,
        required=True,
        help="Path to the training data JSON file (preprocessed in HallucinationSample format)",
    )

    parser.add_argument(
        "--model-name",
        type=str,
        default="answerdotai/ModernBERT-base",
        help="Name or path of the pretrained model",
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default="output/hallucination_detector",
        help="Directory to save the trained model",
    )
    parser.add_argument(
        "--batch-size", type=int, default=4, help="Batch size for training and testing"
    )
    parser.add_argument(
        "--epochs", type=int, default=6, help="Number of training epochs")
    parser.add_argument(
        "--learning-rate", type=float, default=1e-5, help="Learning rate for training"
    )
    return parser.parse_args()


def split_train_dev(
    samples: list[HallucinationSample], dev_ratio: float = 0.1, seed: int = 42
) -> tuple[list[HallucinationSample], list[HallucinationSample]]:
    """Split the samples into train and dev sets.

    :param samples: List of HallucinationSample objects.
    :param dev_ratio: Ratio of the dev set.
    :param seed: Seed for the random number generator.
    :return: Tuple of train and dev sets.
    """
    random.seed(seed)
    random.shuffle(samples)
    dev_size = int(len(samples) * dev_ratio)
    train_samples = samples[:-dev_size]
    dev_samples = samples[-dev_size:]
    return train_samples, dev_samples


def load_dataset(data_path: str) -> HallucinationData:
    """Load dataset from JSON file."""
    path = Path(data_path)
    if not path.exists():
        raise FileNotFoundError(f"Dataset file not found: {data_path}")

    print(f"Loading dataset from {data_path}")
    return HallucinationData.from_json(json.loads(path.read_text()))


def main():
    # Set seeds for reproducibility
    set_seed(123)

    args = parse_args()

    if not args.data_path:
        raise ValueError("--data-path is required. Please specify the path to your training dataset.")

    # Load dataset
    data = load_dataset(args.data_path)
    train_samples = [sample for sample in data.samples if sample.split == "train"]
    train_samples, dev_samples = split_train_dev(train_samples)

    tokenizer = AutoTokenizer.from_pretrained(args.model_name, trust_remote_code=True)
    data_collator = DataCollatorForTokenClassification(tokenizer=tokenizer, label_pad_token_id=-100)

    train_dataset = HallucinationDataset(train_samples, tokenizer)
    dev_dataset = HallucinationDataset(dev_samples, tokenizer)

    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        collate_fn=data_collator,
    )
    dev_loader = DataLoader(
        dev_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        collate_fn=data_collator,
    )

    model = AutoModelForTokenClassification.from_pretrained(
        args.model_name, num_labels=2, trust_remote_code=True
    )

    trainer = Trainer(
        model=model,
        tokenizer=tokenizer,
        train_loader=train_loader,
        test_loader=dev_loader,
        epochs=args.epochs,
        learning_rate=args.learning_rate,
        save_path=args.output_dir,
    )

    trainer.train()


if __name__ == "__main__":
    main()
